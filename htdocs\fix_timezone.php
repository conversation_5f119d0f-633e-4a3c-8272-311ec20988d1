<?php
/**
 * 網頁版時區修復工具
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

echo "<h2>修復時區問題</h2>";

try {
    $pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "<p>正在修復時區問題...</p>";
    
    // 1. 修改 users 表
    echo "<h3>修改 users 表的時間欄位</h3>";
    
    try {
        $alterUsers = "
            ALTER TABLE users 
            MODIFY COLUMN created_at DATETIME NOT NULL,
            MODIFY COLUMN updated_at DATETIME NOT NULL
        ";
        
        $pdo->exec($alterUsers);
        echo "<p>✓ users 表修改完成</p>";
    } catch (PDOException $e) {
        echo "<p>❌ users 表修改失敗: " . $e->getMessage() . "</p>";
    }
    
    // 2. 修改 profiles 表
    echo "<h3>修改 profiles 表的時間欄位</h3>";
    
    try {
        $alterProfiles = "
            ALTER TABLE profiles 
            MODIFY COLUMN created_at DATETIME NOT NULL,
            MODIFY COLUMN updated_at DATETIME NOT NULL
        ";
        
        $pdo->exec($alterProfiles);
        echo "<p>✓ profiles 表修改完成</p>";
    } catch (PDOException $e) {
        echo "<p>❌ profiles 表修改失敗: " . $e->getMessage() . "</p>";
    }
    
    // 3. 驗證修改結果
    echo "<h3>驗證修改結果</h3>";
    
    $tables = ['users', 'profiles'];
    foreach ($tables as $table) {
        echo "<h4>{$table} 表結構</h4>";
        echo "<table border='1'>";
        echo "<tr><th>欄位</th><th>類型</th></tr>";
        
        $stmt = $pdo->query("DESCRIBE {$table}");
        while ($row = $stmt->fetch()) {
            if (in_array($row['Field'], ['created_at', 'updated_at'])) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
    }
    
    echo "<h3>✓ 時區問題修復完成！</h3>";
    echo "<p>現在 created_at 和 updated_at 欄位使用 DATETIME 類型，不會自動轉換時區。</p>";
    echo "<p>請測試註冊新用戶，時間應該會正確顯示為台北時間。</p>";
    
} catch (PDOException $e) {
    echo "<p>❌ 修復失敗: " . $e->getMessage() . "</p>";
}

echo "<br><a href='check_table_structure.php'>檢查表結構</a>";
?>
