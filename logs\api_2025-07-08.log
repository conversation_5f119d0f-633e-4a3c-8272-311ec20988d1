{"timestamp":"2025-07-08 14:12:01","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 14:12:01","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 14:12:01","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:02:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:02:12","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:02:12","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:44","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:44","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:44","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:29:45","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:57:36","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:57:36","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:57:36","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:58:07","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:58:07","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:58:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:59:42","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:59:42","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 15:59:43","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:02:22","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:02:22","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:02:22","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:10:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:48","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:14:48","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:17:20","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:17:21","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:17:21","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:21:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:21:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:21:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:25:49","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:25:50","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:25:50","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:29:50","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:29:50","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 16:29:50","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 17:00:02","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 17:00:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 17:00:03","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:44:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"*************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:44:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"*************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:44:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"*************","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:50:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:50:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 18:50:28","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:05:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:05:59","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:06:00","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:11:49","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:11:49","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:11:49","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:15:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:15:10","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:15:11","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:28:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:28:08","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:28:09","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:32:58","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:32:58","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:32:58","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:39:13","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:39:13","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:39:13","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:43:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:43:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:43:47","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:45:30","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:45:31","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 19:45:31","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 20:06:02","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 20:06:02","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
{"timestamp":"2025-07-08 20:06:02","method":"GET","url":"https:\/\/attendance.app.tn\/backend\/api\/test","http_code":0,"success":false,"user_ip":"2402:7500:505:fd93:6d81:83a:b789:71f3","user_id":null,"error":"Invalid JSON response: Syntax error"}
