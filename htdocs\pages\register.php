<?php
/**
 * SignAttend PHP Frontend - 註冊頁面
 * 版本: 2.0.0 - 使用 API 進行註冊
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 啟動 Session
session_start();

// 載入環境配置檔案
require_once __DIR__ . '/../config/environment.php';

// 載入共用函數
require_once INCLUDES_PATH . '/functions.php';

// 載入核心類別
require_once UTILS_PATH . '/ApiClient.php';
require_once UTILS_PATH . '/Auth.php';

// 初始化核心物件
$apiClient = new ApiClient();
$auth = new Auth();

// 如果已經登入，重定向到儀表板
if ($auth->isLoggedIn()) {
    header('Location: ' . page_url('pages/dashboard.php'));
    exit;
}

// 處理註冊表單提交
$error = '';
$success = '';

// 測試日誌寫入
$testLogFile = __DIR__ . '/../logs/register_test_' . date('Y-m-d') . '.log';
if (!file_exists(dirname($testLogFile))) {
    mkdir(dirname($testLogFile), 0755, true);
}
file_put_contents($testLogFile, "Register page loaded at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    file_put_contents($testLogFile, "POST request received at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND | LOCK_EX);
    // 除錯日誌
    $debugLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'action' => 'form_submitted',
        'post_data' => $_POST,
        'auth_object' => is_object($auth) ? 'exists' : 'null'
    ];

    $logFile = LOG_PATH . '/register_form_debug_' . date('Y-m-d') . '.log';
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    // 基本驗證
    if (empty($name) || empty($email) || empty($password) || empty($confirmPassword)) {
        $error = '請填寫所有必填欄位';
        $debugLog['action'] = 'validation_failed';
        $debugLog['error'] = 'empty_fields';
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    } elseif ($password !== $confirmPassword) {
        $error = '密碼確認不一致';
        $debugLog['action'] = 'validation_failed';
        $debugLog['error'] = 'password_mismatch';
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    } elseif (strlen($password) < 6) {
        $error = '密碼長度至少需要6個字符';
        $debugLog['action'] = 'validation_failed';
        $debugLog['error'] = 'password_too_short';
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = '請輸入有效的電子郵件地址';
        $debugLog['action'] = 'validation_failed';
        $debugLog['error'] = 'invalid_email';
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);
    } else {
        $debugLog['action'] = 'validation_passed';
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

        // 使用 ApiClient 進行註冊
        $result = $auth->register($name, $email, $password);

        $debugLog['action'] = 'register_result';
        $debugLog['result'] = $result;
        file_put_contents($logFile, json_encode($debugLog, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND | LOCK_EX);

        if ($result['success']) {
            $success = '註冊成功！請使用您的帳號登入。';
        } else {
            $error = $result['message'] ?? '註冊失敗，請稍後再試';
        }
    }
}

// 設定頁面變數
$pageTitle = '免費註冊 - ' . APP_NAME;
$pageDescription = '註冊 ' . APP_NAME . ' 帳號，開始使用智慧簽到系統';
$currentPage = 'register';
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?= h($pageDescription) ?>">
    <title><?= h($pageTitle) ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="<?= asset_url('css/custom.css') ?>" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="register-card p-5">
                    <!-- Logo 區域 -->
                    <div class="text-center mb-4">
                        <h1 class="h3 fw-bold text-primary mb-2">
                            <i class="bi bi-person-plus-fill me-2"></i>
                            免費註冊
                        </h1>
                        <p class="text-muted">加入 <?= h(APP_NAME) ?>，開始使用智慧簽到系統</p>
                    </div>

                    <!-- 錯誤/成功訊息 -->
                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <?= h($error) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle-fill me-2"></i>
                            <?= h($success) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        
                    <?php else: ?>
                        <!-- 註冊表單 -->
                        <form method="POST" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="name" class="form-label fw-semibold">
                                    <i class="bi bi-person me-1"></i>姓名
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= h($_POST['name'] ?? '') ?>" required>
                                <div class="invalid-feedback">請輸入您的姓名</div>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope me-1"></i>電子郵件
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= h($_POST['email'] ?? '') ?>" required>
                                <div class="invalid-feedback">請輸入有效的電子郵件地址</div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="bi bi-lock me-1"></i>密碼
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <div class="form-text">密碼長度至少需要6個字符</div>
                                <div class="invalid-feedback">請輸入至少6個字符的密碼</div>
                            </div>

                            <div class="mb-4">
                                <label for="confirm_password" class="form-label fw-semibold">
                                    <i class="bi bi-lock-fill me-1"></i>確認密碼
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       minlength="6" required>
                                <div class="invalid-feedback">請再次輸入密碼</div>
                            </div>

                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus me-2"></i>立即註冊
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>

                    

                    <!-- 返回首頁 -->
                    <div class="text-center mt-4">
                        <a href="<?= page_url('index.php') ?>" class="text-muted text-decoration-none">
                            <i class="bi bi-arrow-left me-1"></i>返回首頁
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 表單驗證 -->
    <script>
        // Bootstrap 表單驗證
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 密碼確認驗證
        const confirmPasswordField = document.getElementById('confirm_password');
        if (confirmPasswordField) {
            confirmPasswordField.addEventListener('input', function() {
                const password = document.getElementById('password').value;
                const confirmPassword = this.value;

                if (password !== confirmPassword) {
                    this.setCustomValidity('密碼確認不一致');
                } else {
                    this.setCustomValidity('');
                }
            });
        }
    </script>
</body>
</html>