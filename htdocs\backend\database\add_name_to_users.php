<?php
/**
 * 資料庫遷移腳本：在 users 表中添加 name 欄位
 */

$dbConfig = require __DIR__ . '/../config/database.php';

try {
    $pdo = new PDO(
        'mysql:host=' . $dbConfig['host'] . ';dbname=' . $dbConfig['database'] . ';charset=utf8mb4',
        $dbConfig['user'],
        $dbConfig['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    echo "正在檢查 users 表結構...\n";
    
    // 檢查 name 欄位是否已存在
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    $nameColumnExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'name') {
            $nameColumnExists = true;
            break;
        }
    }
    
    if ($nameColumnExists) {
        echo "✓ name 欄位已存在於 users 表中\n";
    } else {
        echo "正在添加 name 欄位到 users 表...\n";
        
        // 添加 name 欄位
        $sql = "ALTER TABLE users ADD COLUMN name VARCHAR(255) NULL AFTER email";
        $pdo->exec($sql);
        
        echo "✓ name 欄位已成功添加到 users 表\n";
        
        // 從 profiles 表同步現有的 name 資料到 users 表
        echo "正在同步現有的 name 資料...\n";
        
        $syncSql = "
            UPDATE users u 
            INNER JOIN profiles p ON u.id = p.id 
            SET u.name = p.name 
            WHERE u.name IS NULL AND p.name IS NOT NULL
        ";
        
        $result = $pdo->exec($syncSql);
        echo "✓ 已同步 {$result} 筆 name 資料從 profiles 表到 users 表\n";
    }
    
    // 顯示更新後的表結構
    echo "\n=== Users 表結構 ===\n";
    $stmt = $pdo->query("DESCRIBE users");
    while ($row = $stmt->fetch()) {
        echo sprintf("%-15s %-20s %-10s %-10s\n", 
            $row['Field'], 
            $row['Type'], 
            $row['Null'], 
            $row['Key']
        );
    }
    
    echo "\n✓ 遷移完成！\n";
    
} catch (PDOException $e) {
    echo "❌ 遷移失敗: " . $e->getMessage() . "\n";
    exit(1);
}
?>
