{
    "timestamp": "2025-07-08 19:53:58",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:53:58",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:53:58",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0b6f6f9f6_1751976815",
            "email": "<EMAIL>",
            "name": "aaa"
        }
    }
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "form_submitted",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "validation_passed",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "register_result",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0b8456a0b_1751976836",
            "email": "<EMAIL>",
            "name": "bbb"
        }
    }
}
{
    "timestamp": "2025-07-08 20:26:40",
    "action": "form_submitted",
    "post_data": {
        "name": "ccc",
        "email": "<EMAIL>",
        "password": "cccccc",
        "confirm_password": "cccccc"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:26:40",
    "action": "validation_passed",
    "post_data": {
        "name": "ccc",
        "email": "<EMAIL>",
        "password": "cccccc",
        "confirm_password": "cccccc"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:26:40",
    "action": "register_result",
    "post_data": {
        "name": "ccc",
        "email": "<EMAIL>",
        "password": "cccccc",
        "confirm_password": "cccccc"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0e80b0abc_1751977600",
            "email": "<EMAIL>",
            "name": "ccc"
        }
    }
}
{
    "timestamp": "2025-07-08 20:29:11",
    "action": "form_submitted",
    "post_data": {
        "name": "ddd",
        "email": "<EMAIL>",
        "password": "dddddd",
        "confirm_password": "dddddd"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:29:11",
    "action": "validation_passed",
    "post_data": {
        "name": "ddd",
        "email": "<EMAIL>",
        "password": "dddddd",
        "confirm_password": "dddddd"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:29:11",
    "action": "register_result",
    "post_data": {
        "name": "ddd",
        "email": "<EMAIL>",
        "password": "dddddd",
        "confirm_password": "dddddd"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0f17d310e_1751977751",
            "email": "<EMAIL>",
            "name": "ddd"
        }
    }
}
{
    "timestamp": "2025-07-08 20:54:41",
    "action": "form_submitted",
    "post_data": {
        "name": "eee",
        "email": "<EMAIL>",
        "password": "eeeeee",
        "confirm_password": "eeeeee"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:54:41",
    "action": "validation_passed",
    "post_data": {
        "name": "eee",
        "email": "<EMAIL>",
        "password": "eeeeee",
        "confirm_password": "eeeeee"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:54:41",
    "action": "register_result",
    "post_data": {
        "name": "eee",
        "email": "<EMAIL>",
        "password": "eeeeee",
        "confirm_password": "eeeeee"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d1511ee36e_1751979281",
            "email": "<EMAIL>",
            "name": "eee"
        }
    }
}
{
    "timestamp": "2025-07-08 20:58:34",
    "action": "form_submitted",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:58:34",
    "action": "validation_passed",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:58:34",
    "action": "register_result",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d15fa3ecda_1751979514",
            "email": "<EMAIL>",
            "name": "fff"
        }
    }
}
{
    "timestamp": "2025-07-08 21:10:41",
    "action": "form_submitted",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:10:41",
    "action": "validation_passed",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:10:41",
    "action": "register_result",
    "post_data": {
        "name": "fff",
        "email": "<EMAIL>",
        "password": "ffffff",
        "confirm_password": "ffffff"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "此電子郵件已被註冊"
    }
}
{
    "timestamp": "2025-07-08 21:11:07",
    "action": "form_submitted",
    "post_data": {
        "name": "ggg",
        "email": "<EMAIL>",
        "password": "gggggg",
        "confirm_password": "gggggg"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:11:07",
    "action": "validation_passed",
    "post_data": {
        "name": "ggg",
        "email": "<EMAIL>",
        "password": "gggggg",
        "confirm_password": "gggggg"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:11:07",
    "action": "register_result",
    "post_data": {
        "name": "ggg",
        "email": "<EMAIL>",
        "password": "gggggg",
        "confirm_password": "gggggg"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d18eb76852_1751980267",
            "email": "<EMAIL>",
            "name": "ggg"
        }
    }
}
{
    "timestamp": "2025-07-08 21:15:40",
    "action": "form_submitted",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:15:40",
    "action": "validation_passed",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:15:40",
    "action": "register_result",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d19fc92d65_1751980540",
            "email": "<EMAIL>",
            "name": "hhh"
        }
    }
}
{
    "timestamp": "2025-07-08 21:18:54",
    "action": "form_submitted",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:18:54",
    "action": "validation_passed",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:18:54",
    "action": "register_result",
    "post_data": {
        "name": "hhh",
        "email": "<EMAIL>",
        "password": "hhhhhh",
        "confirm_password": "hhhhhh"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "此電子郵件已被註冊"
    }
}
{
    "timestamp": "2025-07-08 21:19:20",
    "action": "form_submitted",
    "post_data": {
        "name": "i",
        "email": "<EMAIL>",
        "password": "iiiiii",
        "confirm_password": "iiiiii"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:19:20",
    "action": "validation_passed",
    "post_data": {
        "name": "i",
        "email": "<EMAIL>",
        "password": "iiiiii",
        "confirm_password": "iiiiii"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 21:19:20",
    "action": "register_result",
    "post_data": {
        "name": "i",
        "email": "<EMAIL>",
        "password": "iiiiii",
        "confirm_password": "iiiiii"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d1ad85990e_1751980760",
            "email": "<EMAIL>",
            "name": "i"
        }
    }
}
