{
    "timestamp": "2025-07-08 19:53:58",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:53:58",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:53:58",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 19:54:17",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:09",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:04:18",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:06:18",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:27",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:07:39",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:10:56",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:11:04",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": false,
        "message": "註冊失敗"
    }
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "form_submitted",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "validation_passed",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:35",
    "action": "register_result",
    "post_data": {
        "name": "aaa",
        "email": "<EMAIL>",
        "password": "aaaaaa",
        "confirm_password": "aaaaaa"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0b6f6f9f6_1751976815",
            "email": "<EMAIL>",
            "name": "aaa"
        }
    }
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "form_submitted",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "validation_passed",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists"
}
{
    "timestamp": "2025-07-08 20:13:56",
    "action": "register_result",
    "post_data": {
        "name": "bbb",
        "email": "<EMAIL>",
        "password": "bbbbbb",
        "confirm_password": "bbbbbb"
    },
    "auth_object": "exists",
    "result": {
        "success": true,
        "message": "註冊成功！請使用您的帳號登入。",
        "data": {
            "user_id": "user_686d0b8456a0b_1751976836",
            "email": "<EMAIL>",
            "name": "bbb"
        }
    }
}
