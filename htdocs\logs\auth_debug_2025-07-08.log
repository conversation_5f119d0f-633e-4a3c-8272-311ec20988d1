{"timestamp":"2025-07-08 19:53:58","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 19:53:58","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 19:53:58","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 19:54:17","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 19:54:17","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 19:54:17","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:04:09","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:04:09","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:04:09","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:04:18","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:04:18","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:04:18","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:06:18","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:06:18","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:06:18","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:07:27","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:07:27","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:07:27","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:07:39","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:07:39","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:07:39","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:10:56","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:10:56","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:10:56","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:11:04","action":"register_start","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:11:04","action":"sending_request","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api"}
{"timestamp":"2025-07-08 20:11:04","action":"response_received","name":"aaa","email":"<EMAIL>","password_length":6,"api_base_url":"https:\/\/attendance.app.tn\/backend\/api","response":{"success":false,"error":"Invalid JSON response: Syntax error","http_code":0}}
{"timestamp":"2025-07-08 20:13:35","action":"register_start_direct_db","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:35","action":"database_connected","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:35","action":"email_available","name":"aaa","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:35","action":"user_created","name":"aaa","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b6f6f9f6_1751976815"}
{"timestamp":"2025-07-08 20:13:35","action":"profile_created","name":"aaa","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b6f6f9f6_1751976815"}
{"timestamp":"2025-07-08 20:13:35","action":"registration_success","name":"aaa","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b6f6f9f6_1751976815"}
{"timestamp":"2025-07-08 20:13:56","action":"register_start_direct_db","name":"bbb","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:56","action":"database_connected","name":"bbb","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:56","action":"email_available","name":"bbb","email":"<EMAIL>","password_length":6}
{"timestamp":"2025-07-08 20:13:56","action":"user_created","name":"bbb","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b8456a0b_1751976836"}
{"timestamp":"2025-07-08 20:13:56","action":"profile_created","name":"bbb","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b8456a0b_1751976836"}
{"timestamp":"2025-07-08 20:13:56","action":"registration_success","name":"bbb","email":"<EMAIL>","password_length":6,"user_id":"user_686d0b8456a0b_1751976836"}
