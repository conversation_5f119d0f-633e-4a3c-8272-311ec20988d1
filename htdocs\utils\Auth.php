<?php
/**
 * 認證管理類別
 * 負責使用者登入、登出、權限檢查等功能
 */

class Auth {
    private $apiClient;
    
    public function __construct() {
        // 延遲載入 ApiClient，避免依賴問題
        $this->apiClient = null;
    }

    /**
     * 獲取 API 客戶端實例
     */
    private function getApiClient() {
        if ($this->apiClient === null) {
            if (class_exists('ApiClient')) {
                $this->apiClient = new ApiClient();
            } else {
                // 如果 ApiClient 不可用，返回 null
                return null;
            }
        }
        return $this->apiClient;
    }

    /**
     * 獲取資料庫連接
     */
    private function getDatabase() {
        try {
            $pdo = new PDO(
                'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4, time_zone = '+08:00'"
                ]
            );
            return $pdo;
        } catch (PDOException $e) {
            error_log('Database connection failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 生成用戶 ID
     */
    private function generateUserId() {
        return 'user_' . uniqid() . '_' . time();
    }

    /**
     * 直接使用資料庫進行登入驗證
     */
    public function login($email, $password, $rememberMe = false) {
        try {
            $apiClient = $this->getApiClient();
            if (!$apiClient) {
                return [
                    'success' => false,
                    'message' => 'API 客戶端不可用'
                ];
            }

            $response = $apiClient->post('/login', [
                'email' => $email,
                'password' => $password
            ]);
            
            if ($response['success'] && isset($response['data']['status']) && $response['data']['status'] === 'success') {
                $userData = $response['data']['data'];
                
                // 儲存使用者資訊到 Session
                $_SESSION['user'] = $userData['user'];
                $_SESSION['profile'] = $userData['profile'];
                $_SESSION['auth_token'] = $userData['token'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                
                // 設定 API 客戶端的 Token
                $this->apiClient->setToken($userData['token']);
                
                // 如果選擇記住我，設定 Cookie
                if ($rememberMe) {
                    $this->setRememberMeCookie($userData['user']['id'], $userData['token']);
                }
                
                // 記錄登入日誌
                $this->logActivity('login', '使用者登入成功');
                
                return [
                    'success' => true,
                    'message' => '登入成功',
                    'user' => $userData['user']
                ];
            } else {
                // 記錄 API 登入失敗日誌
                $this->logActivity('login_failed', 'API 登入失敗：' . ($response['data']['message'] ?? '登入失敗'), $email);

                return [
                    'success' => false,
                    'message' => $response['data']['message'] ?? '登入失敗'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '登入過程發生錯誤：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 使用者註冊
     */
    public function register($name, $email, $password) {
        try {
            // 除錯日誌
            $logFile = __DIR__ . '/../logs/auth_debug_' . date('Y-m-d') . '.log';
            if (!file_exists(dirname($logFile))) {
                mkdir(dirname($logFile), 0755, true);
            }

            $debugLog = [
                'timestamp' => date('Y-m-d H:i:s'),
                'action' => 'register_start_direct_db',
                'name' => $name,
                'email' => $email,
                'password_length' => strlen($password)
            ];
            file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

            // 直接連接資料庫進行註冊
            $db = $this->getDatabase();
            if (!$db) {
                $debugLog['action'] = 'database_connection_failed';
                file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

                return [
                    'success' => false,
                    'message' => '資料庫連接失敗'
                ];
            }

            $debugLog['action'] = 'database_connected';
            file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

            // 檢查電子郵件是否已存在
            $checkQuery = "SELECT id FROM users WHERE email = :email";
            $checkStmt = $db->prepare($checkQuery);
            $checkStmt->bindParam(':email', $email);
            $checkStmt->execute();

            if ($checkStmt->rowCount() > 0) {
                $debugLog['action'] = 'email_already_exists';
                file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

                return [
                    'success' => false,
                    'message' => '此電子郵件已被註冊'
                ];
            }

            $debugLog['action'] = 'email_available';
            file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

            // 開始交易
            $db->beginTransaction();

            try {
                // 生成用戶 ID
                $userId = $this->generateUserId();

                // 創建使用者
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $userQuery = "INSERT INTO users (id, email, password_hash, created_at, updated_at) VALUES (:id, :email, :password_hash, :created_at, :updated_at)";
                $userStmt = $db->prepare($userQuery);

                $now = date('Y-m-d H:i:s');
                $userStmt->bindParam(':id', $userId);
                $userStmt->bindParam(':email', $email);
                $userStmt->bindParam(':password_hash', $hashedPassword);
                $userStmt->bindParam(':created_at', $now);
                $userStmt->bindParam(':updated_at', $now);

                if (!$userStmt->execute()) {
                    throw new Exception('使用者創建失敗');
                }

                $debugLog['action'] = 'user_created';
                $debugLog['user_id'] = $userId;
                file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

                // 創建個人資料
                $profileQuery = "INSERT INTO profiles (id, name, email, created_at, updated_at) VALUES (:id, :name, :email, :created_at, :updated_at)";
                $profileStmt = $db->prepare($profileQuery);

                $profileStmt->bindParam(':id', $userId);
                $profileStmt->bindParam(':name', $name);
                $profileStmt->bindParam(':email', $email);
                $profileStmt->bindParam(':created_at', $now);
                $profileStmt->bindParam(':updated_at', $now);

                if (!$profileStmt->execute()) {
                    throw new Exception('個人資料創建失敗');
                }

                $debugLog['action'] = 'profile_created';
                file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

                // 提交交易
                $db->commit();

                $debugLog['action'] = 'registration_success';
                file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

                return [
                    'success' => true,
                    'message' => '註冊成功！請使用您的帳號登入。',
                    'data' => [
                        'user_id' => $userId,
                        'email' => $email,
                        'name' => $name
                    ]
                ];

            } catch (Exception $e) {
                // 回滾交易
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            $debugLog['action'] = 'exception';
            $debugLog['error'] = $e->getMessage();
            $debugLog['trace'] = $e->getTraceAsString();
            file_put_contents($logFile, json_encode($debugLog) . "\n", FILE_APPEND | LOCK_EX);

            return [
                'success' => false,
                'message' => '註冊過程發生錯誤：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 使用者登出
     */
    public function logout() {
        // 記錄登出日誌
        $this->logActivity('logout', '使用者登出');
        
        // 清除 Session
        session_unset();
        session_destroy();
        
        // 清除記住我 Cookie
        $this->clearRememberMeCookie();
        
        // 清除 API Token（如果存在）
        if ($this->apiClient && method_exists($this->apiClient, 'clearToken')) {
            if ($this->apiClient && method_exists($this->apiClient, 'clearToken')) {
                $this->apiClient->clearToken();
            }
        }
        
        // 重新開始 Session
        session_start();
        
        return true;
    }
    
    /**
     * 檢查使用者是否已登入
     */
    public function isLoggedIn() {
        // 檢查 Session 中是否有使用者資訊
        if (!isset($_SESSION['user']) || !isset($_SESSION['auth_token'])) {
            return false;
        }
        
        // 檢查 Session 是否過期
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }
        
        // 更新最後活動時間
        $_SESSION['last_activity'] = time();
        
        return true;
    }
    
    /**
     * 要求使用者登入
     */
    public function requireAuth($redirectUrl = null) {
        if (!$this->isLoggedIn()) {
            $redirectUrl = $redirectUrl ?: $_SERVER['REQUEST_URI'];
            $_SESSION['redirect_after_login'] = $redirectUrl;
            
            header('Location: ' . page_url('pages/login.php'));
            exit;
        }
    }
    
    /**
     * 取得當前使用者資訊
     */
    public function getCurrentUser() {
        return $_SESSION['user'] ?? null;
    }
    
    /**
     * 取得當前使用者個人資料
     */
    public function getCurrentProfile() {
        return $_SESSION['profile'] ?? null;
    }
    
    /**
     * 檢查使用者權限
     */
    public function hasPermission($permission) {
        $user = $this->getCurrentUser();
        if (!$user) return false;
        
        // 目前簡單的權限檢查，可以根據需要擴展
        $userRole = $user['role'] ?? 'user';
        
        $permissions = [
            'admin' => ['*'], // 管理員有所有權限
            'user' => ['view_meetings', 'create_meetings', 'manage_attendees']
        ];
        
        $userPermissions = $permissions[$userRole] ?? [];
        
        return in_array('*', $userPermissions) || in_array($permission, $userPermissions);
    }
    
    /**
     * 設定記住我 Cookie
     */
    private function setRememberMeCookie($userId, $token) {
        $cookieValue = base64_encode(json_encode([
            'user_id' => $userId,
            'token' => $token,
            'expires' => time() + COOKIE_LIFETIME
        ]));
        
        setcookie(
            'remember_me',
            $cookieValue,
            time() + COOKIE_LIFETIME,
            '/',
            '',
            false, // HTTPS only in production
            true   // HTTP only
        );
    }
    
    /**
     * 清除記住我 Cookie
     */
    private function clearRememberMeCookie() {
        setcookie('remember_me', '', time() - 3600, '/');
    }
    
    /**
     * 檢查記住我 Cookie
     */
    public function checkRememberMe() {
        if (!isset($_COOKIE['remember_me'])) {
            return false;
        }
        
        try {
            $cookieData = json_decode(base64_decode($_COOKIE['remember_me']), true);
            
            if (!$cookieData || $cookieData['expires'] < time()) {
                $this->clearRememberMeCookie();
                return false;
            }
            
            // 嘗試使用 Token 自動登入
            $this->apiClient->setToken($cookieData['token']);
            
            // 驗證 Token 是否仍然有效
            $response = $this->apiClient->get('/user/profile');
            
            if ($response['success']) {
                // Token 有效，設定 Session
                $_SESSION['user'] = $response['data']['user'];
                $_SESSION['profile'] = $response['data']['profile'];
                $_SESSION['auth_token'] = $cookieData['token'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                
                return true;
            } else {
                $this->clearRememberMeCookie();
                return false;
            }
            
        } catch (Exception $e) {
            $this->clearRememberMeCookie();
            return false;
        }
    }
    
    /**
     * 記錄使用者活動到資料庫
     */
    private function logActivity($action, $description, $email = null) {
        try {
            // 載入 DatabaseLogger
            require_once UTILS_PATH . '/DatabaseLogger.php';
            $logger = new DatabaseLogger();

            $userId = $_SESSION['user']['id'] ?? null;
            $userEmail = $email ?? ($_SESSION['user']['email'] ?? null);
            $success = strpos($action, 'failed') === false; // 如果動作不包含 'failed'，視為成功
            $details = is_array($description) ? $description : ['description' => $description];
            $description = is_array($description) ? json_encode($description, JSON_UNESCAPED_UNICODE) : $description;

            return $logger->logActivity($action, $description, $userEmail, $userId, $success, $details);

        } catch (Exception $e) {
            error_log("記錄認證活動時發生錯誤: " . $e->getMessage());

            // 如果 DatabaseLogger 失敗，回退到原始的檔案日誌方式
            return $this->fallbackFileLog($action, $description, $email);
        }
    }

    /**
     * 回退的檔案日誌記錄方法
     */
    private function fallbackFileLog($action, $description, $email = null) {
        try {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'user_id' => $_SESSION['user']['id'] ?? null,
                'user_email' => $email ?? ($_SESSION['user']['email'] ?? null),
                'action' => $action,
                'description' => $description,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'details' => $description // fallback log already has description as a string
            ];

            $logMessage = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
            $logFile = LOG_PATH . '/auth_' . date('Y-m-d') . '.log';

            $result = file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);

            if ($result === false) {
                $result = file_put_contents($logFile, $logMessage, FILE_APPEND);
            }

            return $result !== false;

        } catch (Exception $e) {
            error_log("回退檔案日誌記錄失敗: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 產生 CSRF Token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
            $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
        }
        return $_SESSION[CSRF_TOKEN_NAME];
    }
    
    /**
     * 驗證 CSRF Token
     */
    public function validateCSRFToken($token) {
        return isset($_SESSION[CSRF_TOKEN_NAME]) && 
               hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    }
    
    /**
     * 取得 CSRF Token 隱藏欄位 HTML
     */
    public function getCSRFTokenField() {
        $token = $this->generateCSRFToken();
        return '<input type="hidden" name="' . CSRF_TOKEN_NAME . '" value="' . htmlspecialchars($token) . '">';
    }
}
?>


