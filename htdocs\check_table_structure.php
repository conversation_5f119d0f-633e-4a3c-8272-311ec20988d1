<?php
/**
 * 檢查 users 表結構
 */

// 定義應用程式初始化常數
define('SIGNATTEND_INIT', true);

// 載入環境配置檔案
require_once __DIR__ . '/config/environment.php';

try {
    $pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<h2>Users 表結構</h2>";
    
    $stmt = $pdo->query("DESCRIBE users");
    echo "<table border='1'>";
    echo "<tr><th>欄位</th><th>類型</th><th>Null</th><th>Key</th><th>預設值</th><th>Extra</th></tr>";
    
    while ($row = $stmt->fetch()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>解決方案</h2>";
    echo "<p>如果 created_at 和 updated_at 是 TIMESTAMP 類型，它們會自動根據資料庫時區轉換。</p>";
    echo "<p>我們需要將它們改為 DATETIME 類型，或者調整插入策略。</p>";
    
} catch (PDOException $e) {
    echo "錯誤: " . $e->getMessage();
}
?>
